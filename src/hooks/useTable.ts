import { ref, reactive, watch, computed } from "vue";
import { cloneDeep } from "lodash";
import { MessagePlugin } from "tdesign-vue-next";

import { useFilterParams } from "@/hooks/useFilterOptions";
import { useTableSelection } from "@/hooks/useTableSelection";
import { exportExcel, clearArray } from "@/utils/fn";

interface UseTableParams {
    // 请求接口
    api: (...args: any) => any;
    // 表格头
    columns: any[] | any;
    // 过滤参数
    filterOptions?: AnyObject;
    // 分页配置
    pageSizeOptions?: (number | { label: string; value: number })[];
    // 时间参数的起始与结束字段
    timeKeys?: [string, string];
    // 初始化后是否自动请求一次数据
    init?: boolean;
    // 时间参数是否使用时间戳格式
    useTime?: boolean;
    // 导出的文件名称
    exportFileName?: any | string;
    // 表格每行的KEY取哪个字段，可选择时用到
    rowKey?: string;
    // 表格选择类型
    selectType?: "checkbox" | "radio";
    // 最大选择数
    selectMax?: number;
    // 初始化后是否显示选择框
    showSelection?: boolean;
    // 是否隐藏全选
    hideSelectAll?: boolean;
    // 页面切换时是否清空选择
    pageChangeClearSelect?: boolean;
    // 过滤请求参数
    filterApiParams?: (params: AnyObject) => AnyObject;
    // 过滤导出时的Columns
    exportColumnsFilter?: (columns: any[]) => any[];
    // 表格与导出共用的过滤方法
    itemFilter?: (...args: any) => any;
    // 导出专用的过滤方式，与itemFilter作用一样，权重更高
    exportFilter?: (...args: any) => any;
}

// 使用表格请求数据
export const useTable = <T = any>({
    columns,
    filterOptions,
    pageSizeOptions = [50, 100, 200, 500],
    timeKeys,
    api,
    filterApiParams = (params) => params,
    exportColumnsFilter,
    itemFilter,
    exportFilter,
    init,
    useTime,
    exportFileName,
    rowKey,
    selectType,
    selectMax = 0,
    showSelection,
    hideSelectAll,
    pageChangeClearSelect = false,
}: UseTableParams) => {
    // 筛选条件
    let { filterParams, resetFilterParams, changeDate, ...otherExport } = useFilterParams({ loading: false, pageNo: 1, pageSize: 10, ...filterOptions }, timeKeys, useTime);

    // 分页配置
    let pagination: any = reactive({
        current: filterParams.pageNo,
        pageSize: filterParams.pageSize,
        total: 0,
        showJumper: true,
        size: "small",
        pageSizeOptions,
        onChange: ({ current, pageSize }: any) => {
            pagination.current = filterParams.pageNo = current;
            pagination.pageSize = filterParams.pageSize = pageSize;
            getList();
        },
    });

    let __columns__ = columns.value ? columns : ref(columns);

    let tableData = reactive({
        list: [] as T[],
        columns: computed({
            get: () => __columns__.value.filter((v: any) => v.visible !== false),
            set: (value) => clearArray(__columns__.value, value),
        }),
        __columns__: computed({
            get: () => __columns__.value,
            set: (value) => clearArray(__columns__.value, value),
        }),
        pagination,
    });

    // 更新页码
    watch(
        () => filterParams.pageNo,
        () => (pagination.current = filterParams.pageNo),
    );

    // 更新页数
    watch(
        () => filterParams.pageSize,
        () => (pagination.pageSize = filterParams.pageSize),
    );

    let requestError = false;
    let inited = false;

    // 获取经销商奖励列表
    let getList = async (filter?: any) => {
        // 是否需要清空选择
        if (inited && pageChangeClearSelect) {
            // eslint-disable-next-line no-use-before-define
            resetSelection();
        }

        if (filter) {
            Object.assign(filterParams, filter);
        }

        let { loading, ...params } = filterParams;

        if (loading && !requestError) {
            return;
        }

        filterParams.loading = true;

        try {
            let res = await api(filterApiParams(params));

            // 进行数据过滤
            if (itemFilter) {
                res.list = res.list.map(itemFilter);
            }

            tableData.list = res.list;

            tableData.pagination.total = res.total;

            filterParams.loading = false;

            requestError = false;
        } catch (error) {
            requestError = true;
        }
    };

    // 筛选条件
    let { selecteds, resetSelection, ...tableSelection } = useTableSelection(tableData, { idKey: rowKey, selectType, showSelection, hideSelectAll });

    // 如果有最大选择限制的时候，再进行限制
    if (selectMax > 0) {
        // 当选择的列表或者表格数据发生变化时，进行判断
        watch([selecteds, () => tableData.list], () => {
            // 是否达到了最大选择数量
            let isMax = selecteds.keys.length >= selectMax;

            // 遍历列表，如果达到了最大选择数量，并且当前项不在选择的列表中，则禁用
            tableData.list.forEach((item: any) => {
                item.disabled = isMax && !selecteds.keys.includes(item[tableSelection.rowKey.value]);
            });
        });
    }

    // 导出
    let exportLoading = ref(false);

    // 导出excel方法
    const toExportExcel = async () => {
        let { loading, ...params } = filterParams;

        if (exportLoading.value) {
            return;
        }

        exportLoading.value = true;
        const loadingFlag = MessagePlugin.loading("导出中...");

        let titles = tableData.columns.filter((v: any) => v.colKey !== "action");

        // 过滤导出的列数
        if (exportColumnsFilter) {
            titles = exportColumnsFilter(titles);
        }

        // 公共的导出参数
        let exportParams = { titles, fileName: exportFileName?.value || exportFileName };

        if (selecteds.rows.length) {
            let list = cloneDeep(selecteds.rows);
            // 导出选择的数据
            await exportExcel({ ...exportParams, list: list as any, total: list.length });
        } else {
            // 按筛选条件发请求 导出excel
            await exportExcel({ ...exportParams, total: tableData.pagination.total, api, params: filterApiParams(params) }, exportFilter || itemFilter);
        }

        exportLoading.value = false;
        MessagePlugin.close(loadingFlag);
    };

    // 搜索方法
    let handleSearch = async () => {
        filterParams.pageNo = 1;
        console.log(123)
        await getList();
    };

    // 重置方法
    let handleResetFilter = (reload = true) => {
        resetFilterParams();

        if (reload) {
            getList();
        }
    };

    // 如果需要初始化
    if (init) {
        getList();
        inited = true;
    }

    // 改变时间同时进行重新搜索
    let changeDateAndSearch = (dates: any) => {
        changeDate(dates);
        handleSearch();
    };

    return {
        filterParams: filterParams as any & { loading: boolean },
        resetFilterParams,
        changeDate,
        ...otherExport,
        changeDateAndSearch,
        selecteds,
        resetSelection,
        ...tableSelection,
        pagination,
        tableData,
        getList,
        handleSearch,
        handleResetFilter,
        exportLoading,
        exportExcel: toExportExcel,
    };
};
