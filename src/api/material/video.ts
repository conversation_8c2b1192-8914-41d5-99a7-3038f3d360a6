import type { PagingParams } from "#/api";
import { del, get, post, put } from "@/api";
import { HEADERS_MATERIAL_AI } from "../ServerMap";
import type { Category } from "@/hooks/useCategory.ts";
import { MaterialType } from "@/api/material/index.ts";

interface MaterialQueryParams {
    // 素材类型
    type?: MaterialType;
    // 分类ID
    categoryIds?: Category["id"][];
    // 目录ID
    folderIds?: number[];
    // 素标ID
    id?: string;
    ids?: string[];
    // 创建开始时间
    createTimeStart?: string;
    // 创建结束时间
    createTimeEnd?: string;

    // 任务ID
    taskId?: string;
    // 脚本索引
    scriptIndex?: number;

    // 目录ID
    dirId?: string;
    // 视频规格 1:竖屏 2:横屏 3:不指定
    vertical?: 1 | 2 | 3;

    // 素材类型 1:空镜 2:口播
    videoType?: 1 | 2;
}


// 获取素材列表
export const getMaterialList = (data: MaterialQueryParams) => post(`/api/ai/material/paging`, data, { ...HEADERS_MATERIAL_AI, timeout: 1000 * 30 });

// 搜索素材
export const queryMaterialList = (data: PagingParams<MaterialQueryParams & { keyword: string; duration: number }>) => post(`/api/ai/material/query`, data, HEADERS_MATERIAL_AI);

// 添加素材
export const addMaterial = (urls: string[], categoryIds: Category["id"][]) => post(`/api/ai/material/batch`, { urls, folder_id:categoryIds }, HEADERS_MATERIAL_AI);

// 旋转素材
export const rotateMaterial = async (data: { id: string; rotate: number; }[]) => post( `/api/ai/material/rotate`, data, HEADERS_MATERIAL_AI);

// 删除素材
export const delMaterial = (ids: string[]) => del("/api/ai/material", { ids }, HEADERS_MATERIAL_AI);

