import { get, post, del, put } from "@/api/index";
import { HEADERS_CRM_AI, HEADERS_CRM_AI_LLM,  MATERIAL_AI_YK } from "../ServerMap";
import type { Category } from "@/hooks/useCategory";
import type { PagingParams } from "#/api";
import type { VideoMaterial } from "@/types/generate";

export type MaterialType = "image" | "video";

// interface CategoryParams extends Category {
//     type: MaterialType;
// }

interface MaterialQueryParams {
    // 素材类型
    type?: MaterialType;
    // 分类ID
    categoryIds?: Category["id"][];
    // 素标ID
    id?: string;
    ids?: string[];
    // 创建开始时间
    createTimeStart?: string;
    // 创建结束时间
    createTimeEnd?: string;

    // 任务ID
    taskId?: string;
    // 脚本索引
    scriptIndex?: number;

    // 目录ID
    dirId?: string;
    // 视频规格 1:竖屏 2:横屏 3:不指定
    vertical?: 1 | 2 | 3;

    // 素材类型 1:空镜 2:口播
    videoType?: 1 | 2;
}

const defaultMaterialType: MaterialType = "video";

// 获取推荐视频素材列表
export const getRecommendVideoMaterialList = (data: PagingParams<MaterialQueryParams & { keyword: string; duration: number }>) => get("/api/ai/material/query", data, HEADERS_CRM_AI);

// 获取个人素材列表
export const getMyMaterialList = (params: PagingParams<{ ids?: string[] }>) => get("/api/ai/user/material/list", { type: defaultMaterialType, ...params }, HEADERS_CRM_AI);

// 添加个人素材
export const addMyMaterial = (urls: string[]) => post("/api/ai/user/material", { type: defaultMaterialType, urls }, HEADERS_CRM_AI);


// 旋转素材
export const rotateMaterial = async (data: { materialId: string; rotate: number; materialType: VideoMaterial["materialType"] }[]) => {
    let res = await post(
        "/api/ai/material/rotate",
        data.map((v) => ({ ...v, materialType: v.materialType || "crop_material" })),
        HEADERS_CRM_AI,
    );

    return res.map((v: any) => ({ ...v, errMsg: v.errMsg === "SUCCESS" ? "" : v.errMsg }));
};

// 批量修改素材分类
export const batchUpdateMaterialCategory = (materialIds: string[], categoryIds: Category["id"][]) =>
    put("/api/ai/material/batch/category", { type: defaultMaterialType, materialIds, categoryIds }, { ...HEADERS_CRM_AI, timeout: 30000 });

// 获取视频切片
export const getVideoSlice = (url: string, clipTime: number[]) => post("/api/ai/video/material/clip", { url, clipTime }, HEADERS_CRM_AI);

// 获取口播素材列表
export const getSpeechVideoMaterialList = (data: PagingParams<MaterialQueryParams & { keyword: string }>) => get("/api/ai/material/subtitle-query", data, HEADERS_CRM_AI_LLM);
