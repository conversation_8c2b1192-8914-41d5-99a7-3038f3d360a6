<template>
    <div class="directory-tree-wrapper">
        <div class="header">
            {{ title || (type === "video" ? "视频目录" : "脚本目录") }}

            <t-button theme="primary" v-if="editable" @click="showDialog()">
                <template #icon><AddIcon /></template>
                新建目录
            </t-button>
        </div>

        <div class="directory-content">
            <t-input class="search" :placeholder="placeholder" v-if="search" v-model="keywordModelValue" clearable>
                <template #suffixIcon>
                    <SearchIcon />
                </template>
            </t-input>

            <t-tree
                :key="treeRefreshKey"
                :data="categoryOptions"
                :keys="fieldNames"
                v-model:expanded="expanded"
                hover
                :expandLevel="1"
                :filter="categoryFilter"
                :scroll="{
                    isFixedRowHeight: true,
                    bufferSize: 10,
                    type: 'virtual',
                }"
                @click="handleSelectNode"
            >
                <template #label="{ node }">
                    <span :style="{ color: node.data.id === selectedValue ? '#005FEE' : '' }">{{ node.data.name }}</span>
                </template>

                <template #operations="{ node }">
                    <t-dropdown
                        v-if="editable"
                        class="more"
                        :options="[
                            { content: '添加子目录', value: 'add' },
                            { content: '修改目录', value: 'modify', disabled: node.data.parentId === '-1' },
                        ]"
                        @click="handleSelectMore($event.value, node)"
                    >
                        <t-button variant="text" size="small">
                            <MoreIcon class="moreIcon" />
                        </t-button>
                    </t-dropdown>
                </template>
            </t-tree>
        </div>
    </div>

    <t-dialog class="directory-tree-dialog" width="400px" v-model:visible="formData._.visible" theme="info" :header="`${formData.id ? '修改' : '新建'}目录`">
        <div class="dialog-content">
            <div class="row">
                <div class="title">选择上级目录</div>
                <SelectDirectoryCascader :type="type" v-model:value="formData.parentId" :disabled="!!formData.id" :keys="fieldNames" :categoryTree="categoryOptions" />
            </div>

            <div class="row">
                <div class="title">目录名称</div>
                <t-input placeholder="请输入" clearable v-model="formData.name" />
            </div>
        </div>

        <template #footer>
            <div class="dialog-footer">
                <t-button theme="default" block @click="formData._.visible = false"> 取消 </t-button>
                <t-button theme="primary" block @click="handleConfirm"> 确认 </t-button>
            </div>
        </template>
    </t-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, computed } from "vue";
import { AddIcon, SearchIcon, MoreIcon } from "tdesign-icons-vue-next";
import { TreeProps, TreeNodeModel, MessagePlugin, TreeNodeValue } from "tdesign-vue-next";

import { Category } from "#/generate";
import { treeToMap } from "@/utils/fn";

import SelectDirectoryCascader from "./cascader.vue";
import { createFolder, getFolderList } from "@/api/folder";

const props = withDefaults(
    defineProps<{
        title?: string;
        editable?: boolean;
        search?: boolean;
        placeholder?: string;
    }>(),
    {
        search: true,
        placeholder: "请输入内容",
    },
);

const emit = defineEmits<{
    (e: "select", id: string): void;
}>();

// 当前选中的目录ID
const selectedValue = defineModel<string | number | null>("value");

// addable removable modifiable

const fieldNames = {
    label: "name",
    value: "id",
};

// 搜索关键字
const keywordModelValue = defineModel<string>("keyword");

// 刷新key，用于更新树
const treeRefreshKey = ref(0);

// 过滤器
const categoryFilter = computed<TreeProps["filter"]>(() => (keywordModelValue.value ? (node) => node.data.name.includes(keywordModelValue.value) : undefined));

// 展开的目录
const expanded = ref<TreeNodeValue[]>([]);

// 目录数据
const categoryOptions = ref<Category[]>([]);

// 目录数据map
const categoryMap = computed(() => {
    treeRefreshKey.value++;
    return treeToMap(categoryOptions.value, { parentIdKey: "pid" });
});

// 获取目录数据
getFolderList().then((res) => (categoryOptions.value = res));

const formData = reactive({
    // 内部数据，不发到后端，其他的发到后端
    _: {
        // 是否显示
        visible: false,
    },

    // 目录类型 script=视频脚本 video=成品视频
    type: props.type,

    // 上级目录ID
    parentId: "",
    // 有ID为修改，无ID为新建
    id: "",
    // 目录名称
    name: "",
});

// 显示弹窗
const showDialog = (parentId?: string, id?: string) => {
    formData._.visible = true;
    const name = id ? categoryMap.value.get(id)?.name : "";
    Object.assign(formData, { parentId, id, name });
};

// 确认
const handleConfirm = async () => {
    if (!formData.parentId) {
        return MessagePlugin.warning("请选择上级目录");
    }

    if (!formData.name) {
        return MessagePlugin.warning("请输入目录名称");
    }

    let result = await createFolder({
        name: formData.name,
        pid: formData.parentId,
    });

    if (typeof result === "boolean") {
        getFolderList().then((res) => (categoryOptions.value = res));
        return
    }

    formData._.visible = false;

    if (!expanded.value.includes(formData.parentId)) {
        expanded.value.push(formData.parentId);
    }

    MessagePlugin.success(`${formData.id ? "修改" : "新建"}成功`);
};

// 点击更多
const handleSelectMore = (mod: "add" | "modify" | "delete", node: TreeNodeModel<Category>) => {
    let { id, parentId } = node.data;

    if (mod === "add") {
        showDialog(id);
    } else if (mod === "modify") {
        showDialog(parentId, id);
    } else if (mod === "delete") {
        console.log(mod, id, parentId);
    }
};

// 选中节点
const handleSelectNode = ({ node, e }: { node: TreeNodeModel<Category>; e: any }) => {
    // 点击后面的操作的按钮也会触发。这里做一下过滤
    if (e.target.tagName !== "SPAN") {
        return;
    }

    let id = node.data.id;

    // if (id === selectedValue.value) {
    //     return;
    // }

    selectedValue.value = selectedValue.value === id ? "" : id;
    emit("select", selectedValue.value);
};
</script>

<style scoped lang="less">
.directory-tree-wrapper {
    height: 100%;
    display: flex;
    flex-direction: column;

    padding: 20px;
    background-color: #242b33;
    border-radius: 3px;

    > .header {
        font-weight: 400;
        font-size: 14px;
        color: #ffffff;
        line-height: 22px;
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 20px;
    }

    > .directory-content {
        flex: 1;
        flex-basis: 0;
        overflow: hidden;

        display: flex;
        flex-direction: column;
        width: 208px;
        background-color: #121314;
        border-radius: 6px;
        border: 1px solid rgba(255, 255, 255, 0.2);
        padding: 10px 8px;

        > .search {
            margin-bottom: 10px;
        }

        > .t-tree {
            flex: 1;
            overflow-y: overlay;
        }
    }
}

.directory-tree-dialog {
    .dialog-content {
        padding: 24px 0;

        > .row {
            + .row {
                margin-top: 10px;
            }

            > .title {
                font-weight: 500;
                font-size: 16px;
                line-height: 30px;
                margin-bottom: 10px;

                &:before {
                    content: "*";
                    color: rgba(234, 0, 0, 1);
                    margin-right: 4px;
                }
            }
        }
    }

    .dialog-footer {
        display: flex;
        justify-content: space-between;
    }
}
</style>
