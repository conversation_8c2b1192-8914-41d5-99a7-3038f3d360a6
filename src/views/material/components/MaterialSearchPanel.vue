<template>
    <div class="material-search-panel">
        <div class="panel-container">
            <t-space>
                <t-button :theme="searchType === 'text' ? 'primary' : 'default'" @click="toggleSearchType('text')">
                    <template #icon><TextboxIcon /></template>
                    文字搜索
                </t-button>

                <t-button :theme="searchType === 'image' ? 'primary' : 'default'" @click="toggleSearchType('image')">
                    <template #icon><ImageIcon /></template>
                    图片搜索
                </t-button>
            </t-space>

            <t-space class="filter-section" break-line>
                <!-- 素材分类 -->
                <SelectMaterialCategory v-model:value="filterParams.folderIds[0]" @change="handleSearch" />

                <!-- 素材规格 -->
                <t-select
                    v-model:value="filterParams.vertical"
                    placeholder="素材规格"
                    @change="handleSearch"
                    clearable
                    :options="[
                        { label: '竖屏', value: 1 },
                        { label: '横屏', value: 2 },
                    ]"
                />

                <!-- 创建时间 -->
                <t-date-range-picker allow-input clearable v-model="filterParams.__createTime__" @change="handleSearch" />

                <!-- 搜索时长 -->
                <t-input-number v-model="filterParams.duration" :allowInputOverLimit="false" :max="30" :min="1" :format="(e:any) => `${e}秒`" @change="handleSearch" />

                <!-- 关键词 -->
                <t-input v-if="searchType === 'text'" class="keyword" placeholder="请输入关键词" v-model="filterParams.keyword" clearable @enter="handleSearch">
                    <template #suffixIcon>
                        <SearchIcon />
                    </template>
                </t-input>

                <div v-else-if="searchType === 'image'" class="imageSearch">
                    <t-button theme="primary" size="small" @click="handleUploadImage" :loading="uploadImageData.loading">
                        <template #icon><ImageIcon /></template>
                        {{ filterParams.imageUrl ? "重新上传" : "上传图片" }}
                    </t-button>

                    <template v-if="uploadImageData.fileName">
                        <img :src="uploadImageData.image" />

                        <div class="fileName">
                            <div class="name">{{ uploadImageData.fileName }}</div>
                            <t-loading v-if="uploadImageData.loading && uploadImageData.percent < 100" size="small" :text="`${uploadImageData.percent}%`"></t-loading>
                            <div class="close" v-else @click="clearUploadImage">
                                <CloseIcon />
                            </div>
                        </div>
                    </template>

                    <div class="desc" v-else>请上传jpg、png、gif格式的图片</div>
                </div>

                <t-button @click="handleSearch" :disabled="uploadImageData.loading" :loading="filterParams.loading">搜索</t-button>
            </t-space>

            <div class="content" ref="scrollContainer">
                <div class="videoCardList">
                    <VideoCard
                        class="videoCardItem"
                        v-for="item in list"
                        v-bind="getMaterialStartTimeAndEndTime(item)"
                        v-model:checked="item.checked"
                        :key="item.id"
                        :item="item"
                        image-ratio="1:1"
                        preview-video
                        :selectable="false"
                    >
                        <template #flag>{{ item.clipTime?.[0] }}-{{ item.clipTime?.[1] }}秒</template>

                        <template #operation>
                            <div class="similarity"><StarFilledIcon />{{ Number(item.score?.toFixed?.(2)) }}分</div>

                            <t-button class="operation-item" variant="text" size="small" @click="downloadVideoSlice(item)">
                                <t-loading v-if="item.downloading" />
                                <Download1Icon v-else />
                            </t-button>
                        </template>
                    </VideoCard>
                </div>

                <EmptyIcon v-if="!list.length" text="搜索列表暂无素材" description=" 请在上方搜索视频素材" />
            </div>
        </div>
    </div>
</template>

<script setup lang="tsx">
import { ref, reactive } from "vue";
import { TextboxIcon, ImageIcon, SearchIcon, Download1Icon, StarFilledIcon, CloseIcon } from "tdesign-icons-vue-next";
import { MessagePlugin } from "tdesign-vue-next";

import { useList } from "@/hooks/useList";
import { getVideoSlice } from "@/api/material";
import { getVideoThumbUrl, downloadFile, convertDateParam } from "@/utils/fn";
import { VideoMaterial } from "@/types/generate";
import Upload from "@/utils/upload";

import VideoCard from "@/components/VideoCard/index.vue";
import EmptyIcon from "@/components/EmptyIcon/index.vue";
import SelectMaterialCategory from "@/components/SelectMaterialCategory/index.vue";
import { getMaterialStartTimeAndEndTime } from "@/views/generate/common/utils";
import { queryMaterialList } from "@/api/material/video.ts";

// 搜索类型
const searchType = ref<"text" | "image">("text");

const toggleSearchType = (type: typeof searchType.value) => {
    searchType.value = type;
    list.value = [];
};

const {
    scrollContainer,
    list,
    filterParams,
    handleSearch: onSearch,
} = useList({
    filterOptions: {
        // 每页请求多少
        pageSize: 50,
        // 分类ID
        folderIds: [],
        // 素材规格
        vertical: "",
        // 时间
        __createTime__: [],
        // 搜索时长
        duration: 2,
        // 关键词
        keyword: "",
        // 图片搜索
        imageUrl: "",
    },
    filterApiParams(params) {
        let { __createTime__, ...newParams } = params;
        return { ...newParams, [searchType.value === "text" ? "imageUrl" : "keyword"]: undefined, ...convertDateParam(__createTime__) };
    },
    api: queryMaterialList,
    itemFilter(item: any) {
        return {
            ...item,
            // 缩略图
            image: getVideoThumbUrl(item.url),
            // 标题
            title: item.folderName,

            // 视频切片时间
            clipTime: [Math.floor(item.clipStart / 1000), Math.floor((item.clipStart + item.clipDuration) / 1000)],
        };
    },
});

// 搜索事件
const handleSearch = () => {
    if (!filterParams.duration) {
        MessagePlugin.warning("搜索时长不能小于1秒");
        return;
    }

    if (searchType.value === "text" && !filterParams.keyword) {
        MessagePlugin.warning("请输入关键词");
        return;
    }

    if (searchType.value === "image" && !filterParams.imageUrl) {
        MessagePlugin.warning("请上传图片");
        return;
    }

    onSearch();
};

// 下载视频切片
const downloadVideoSlice = async (item: VideoMaterial & { downloading: boolean; clipTime: number[] }) => {
    item.downloading = true;
    try {
        let url = await getVideoSlice(item.url, item.clipTime);
        downloadFile(url);
    } catch (error) {}
    item.downloading = false;
};

// 上传图片
const uploadImageData = reactive({
    loading: false,
    fileName: "",
    percent: 0,
    image: "",
});
const handleUploadImage = async () => {
    uploadImageData.loading = true;
    try {
        let { url } = await Upload.image(undefined, {
            remotePath: "tmp",
            onProgress: (percent: number) => (uploadImageData.percent = percent),
            onInput: (file: any) => {
                uploadImageData.fileName = file.name;
                uploadImageData.image = URL.createObjectURL(file);
            },
        });
        filterParams.imageUrl = url;
    } catch (error) {}

    uploadImageData.loading = false;
};
const clearUploadImage = () => {
    URL.revokeObjectURL(uploadImageData.image);
    uploadImageData.fileName = "";
    uploadImageData.image = "";
    uploadImageData.percent = 0;
    uploadImageData.loading = false;
    filterParams.imageUrl = "";
};
</script>

<style lang="less" scoped>
.material-search-panel {
    height: 100%;

    > .panel-container {
        height: 100%;
        border-radius: 3px;
        background: linear-gradient(226deg, #1d252a 0%, #1a1d22 100%);
        padding: 20px;

        display: flex;
        flex-direction: column;
        gap: 20px;

        > .filter-section {
            display: flex;
            align-items: center;

            :deep(> .t-space-item) {
                &:has(.keyword),
                &:has(.imageSearch) {
                    flex: 1;
                    overflow: hidden;
                }
            }

            .imageSearch {
                background-color: #121314;
                border-radius: 3px;
                border: 1px solid rgba(255, 255, 255, 0.2);
                padding: 3px;

                display: flex;
                align-items: center;
                gap: 10px;

                > .desc {
                    font-weight: 400;
                    font-size: 12px;
                    color: rgba(255, 255, 255, 0.5);
                    line-height: 22px;
                }

                > img {
                    font-size: 24px;
                    width: 1em;
                    height: 1em;
                    border-radius: 3px;
                }

                > .fileName {
                    flex: 1;
                    display: flex;
                    align-items: center;
                    gap: 10px;
                    overflow: hidden;

                    > .name {
                        overflow: hidden;
                        text-overflow: ellipsis;
                        white-space: nowrap;

                        font-weight: 400;
                        font-size: 12px;
                        color: rgba(255, 255, 255, 0.9);
                    }

                    > .close {
                        font-size: 16px;
                        padding: 4px;
                        cursor: pointer;
                        display: flex;
                        align-items: center;
                        justify-content: center;

                        &:hover {
                            background-color: rgba(255, 255, 255, 0.1);
                            border-radius: 3px;
                        }

                        > .t-icon {
                            font-size: 1em;
                        }
                    }
                }
            }
        }

        > .content {
            flex: 1;
            overflow-y: overlay;

            > .videoCardList {
                display: grid;
                grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
                gap: 20px;

                :deep(.operation-left) {
                    justify-content: space-between;

                    > .similarity {
                        width: 77px;
                        height: 24px;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        gap: 3px;

                        background: linear-gradient(145deg, rgba(54, 110, 244, 0.3) 0%, rgba(72, 193, 187, 0.3) 100%);
                        border-radius: 3px;

                        font-weight: 400;
                        font-size: 14px;
                        line-height: 1em;
                        color: #ffffff;
                    }
                }
            }
        }
    }
}
</style>
