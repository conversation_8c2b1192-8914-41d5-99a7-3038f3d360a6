<template>
    <div class="material-management-panel">
        <!-- <div class="panel-container directory-wrapper"> -->
        <!-- <t-tree></t-tree> -->
        <!-- </div> -->

        <SelectFolder editable v-model:value="folderId" @select="handleTreeSelected" />

        <div class="panel-container content-wrapper">
            <div class="filter-section">
                <t-space :align="'center'">
                    <t-checkbox :checked="checkAll" :indeterminate="indeterminate" @change="handleSelectAll">全选
                    </t-checkbox>
                    <!-- <t-button theme="primary" :disabled="!checkAll && !indeterminate"> 批量移动目录 </t-button> -->
                    <t-button theme="primary" :disabled="!checkAll && !indeterminate"
                              @click="handleModifyDirectory(selectedIds)">批量设置分类
                    </t-button>
                    <t-button theme="primary" :disabled="!checkAll && !indeterminate"
                              @click="handleDelete(selectedIds)">批量删除
                    </t-button>

                    <t-dropdown
                        :options="[
                            { content: '右旋转90度', value: 90 },
                            { content: '左旋转90度', value: 270 },
                        ]"
                        @click="handleBatchRotate(selectedIds, $event.value)"
                    >
                        <t-button theme="primary"
                                  :disabled="(!checkAll && !indeterminate) || selectedIds.some((id) => listMap[id].status === 0)">
                            批量旋转
                        </t-button>
                    </t-dropdown>
                </t-space>

                <!-- 筛选 -->
                <div class="floatBox">
                    <t-select
                        v-model:value="filterParams.vertical"
                        placeholder="素材规格"
                        @change="handleSearch"
                        clearable
                        :options="[
                            { label: '竖屏', value: true },
                            { label: '横屏', value: false },
                        ]"
                    />

                    <t-date-range-picker allow-input clearable @change="changeDateAndSearch" />

                    <t-button theme="primary" @click="uploadVideoDialogVisible = true">
                        <template #icon>
                            <AddIcon />
                        </template>
                        上传视频
                    </t-button>
                </div>
            </div>

            <div class="content" ref="scrollContainer">
                <div class="videoCardList">
                    <VideoCard
                        class="videoCardItem"
                        v-for="item in tableData.list"
                        v-model:checked="item.checked"
                        :key="`${item.materialId}-${item.status}`"
                        :item="item"
                        image-ratio="1:1"
                        preview-video
                        :moreOptions="[
                            { content: '编辑分类', value: 'move' },
                            { content: '右旋转90度', value: 'rotate90', disabled: item.status === 0 },
                            { content: '左旋转90度', value: 'rotate270', disabled: item.status === 0 },
                            { content: '删除素材', value: 'delete', theme: 'error' },
                        ]"
                        @more="handleSelectMore($event, item)"
                    >
                        <template #flag v-if="item.status === 0">画面处理中</template>

                        <template #operation>
                            <t-button class="operation-item" variant="text" size="small"
                                      @click="handleSelectMore('download', item)">
                                <Download1Icon />
                            </t-button>

                            <t-button class="operation-item" variant="text" size="small"
                                      @click="handleSelectMore('move', item)">
                                <EditIcon />
                            </t-button>
                        </template>
                    </VideoCard>
                </div>

                <EmptyIcon v-if="!tableData.list.length" text="暂无素材" />
            </div>

            <t-pagination v-bind="tableData.pagination" />
        </div>
    </div>

    <!-- 上传视频 -->
    <UploadVideoDialog v-if="uploadVideoDialogVisible" v-model:visible="uploadVideoDialogVisible" @goto="emit('goto')"
                       @success="handleSearch" />
</template>

<script setup lang="tsx">
import { ref, computed, watch } from "vue";
import { MessagePlugin, DialogPlugin, Button } from "tdesign-vue-next";
import { AddIcon, Download1Icon, EditIcon } from "tdesign-icons-vue-next";

import { useAllCheckbox } from "@/hooks/useComponent";
import { batchUpdateMaterialCategory } from "@/api/material";
import { getMaterialList, rotateMaterial, delMaterial } from "@/api/material/video.ts";
import { getVideoThumbUrl, downloadFile, sleep } from "@/utils/fn";
import { useTable } from "@/hooks/useTable";

import VideoCard from "@/components/VideoCard/index.vue";
import EmptyIcon from "@/components/EmptyIcon/index.vue";
import SelectMaterialCategory from "@/components/SelectMaterialCategory/index.vue";
import UploadVideoDialog from "./UploadVideoDialog.vue";
import SelectFolder from "@/components/SelectFolder/tree.vue";

const emit = defineEmits<{
    (e: "goto"): void;
}>();

const scrollContainer = ref<HTMLElement | null>(null);

const folderId = ref(null);

const { filterParams, tableData, getList, handleSearch, changeDateAndSearch } = useTable({
    filterOptions: {
        // 每页请求多少
        pageSize: 100,
        // 分类id
        folderIds: [],
        // 素材规格
        vertical: null,
    },
    useTime: false,
    timeKeys: ["createTimeStart", "createTimeEnd"],
    api: getMaterialList,
    columns: [],
    itemFilter(item: any) {
        return {
            ...item,
            // 缩略图
            image: getVideoThumbUrl(item.url),
            // 标题
            title: item.folderName,
        };
    },
});

const handleTreeSelected = () => {
    filterParams.folderIds = folderId.value ? [folderId.value] : []
    handleSearch()
};


// 请求一次，然后轮询查询列表状态
handleSearch().then(() => pollingListStatus());

// 列表map
const listMap = computed(() => Object.fromEntries(tableData.list.map((item) => [item.id, item])));

// 轮询查询列表状态方法
const pollingListStatus = async () => {
    // 判断页面是否已卸载,如果卸载则停止轮询
    if (!document.body.contains(scrollContainer!.value)) {
        return;
    }

    let ids = tableData.list.filter((item) => item.status === 0).map((item) => item.id);

    // 如果没有正在处理中的状态，则停止轮询
    if (!ids.length) {
        return;
    }

    await sleep(3);

    let res = await getMaterialList({ ids });

    res.list.forEach((item: any) => {
        if (item.status !== 0 && listMap.value[item.id]) {
            Object.assign(listMap.value[item.id], item);
        }
    });

    pollingListStatus();
};

// 全选hooks
const {
    selectedIds,
    indeterminate,
    checkAll,
    handleSelectAll,
} = useAllCheckbox({ list: computed(() => tableData.list) });

// 删除
const handleDelete = async (ids: string[]) => {
    let confirm = await new Promise((resolve) => {
        let confirmDia = DialogPlugin({
            theme: "warning",
            header: "提示",
            body: "是否确认删除？",
            confirmBtn: "确定",
            cancelBtn: "取消",
            onConfirm: async () => {
                confirmDia.hide();
                resolve(true);
            },
            onClose: () => {
                confirmDia.hide();
                resolve(false);
            },
        });
    });

    if (!confirm) {
        return;
    }

    await delMaterial(ids);
    MessagePlugin.success("删除成功");
    getList();
};

// 修改视频目录
const handleModifyDirectory = (ids: string[], folderId?: string) => {
    let _folderId = ref(folderId || null);

    let loading = ref(false);
    const confirmDia = DialogPlugin({
        theme: "info",
        header: "编辑分类",

        body: () => (
            <div style="padding: 24px 0;">
                <div class="title" style="font-weight: 500;font-size: 16px;line-height: 30px;margin-bottom: 10px;">
                    <span style="color: rgba(234, 0, 0, 1); margin-right: 4px;">*</span>移动到
                </div>

                <SelectMaterialCategory value="folderId" onUpdate:value={(value) => (_folderId.value = value as any)} />
            </div>
        ),
        footer: () => (
            <div style="display: flex;justify-content: space-between;">
                <Button theme="default" block onClick={() => confirmDia.hide()}>
                    取消
                </Button>

                <Button
                    theme="primary"
                    block
                    loading={loading.value}
                    onClick={async () => {
                        loading.value = true;
                        await batchUpdateMaterialCategory(ids, _folderId.value);
                        MessagePlugin.success("修改成功");
                        confirmDia.hide();
                        getList();
                        loading.value = false;
                    }}
                >
                    确认
                </Button>
            </div>
        ),
        width: "400px",
        placement: "center",
    });
};

// 批量旋转
const handleBatchRotate = async (ids: string[], rotate: 90 | 270) => {
    // 旋转列表
    console.log(listMap);
    let res = await rotateMaterial(ids.map((id) => ({
        id: id,
        rotate: (listMap.value[id].rotate + rotate) % 360,
    })));

    // 成功列表
    let successIds = [];
    // 失败列表
    let errorIds = [];

    res.forEach((v: any) => {
        let item = listMap.value[v.id];
        if (v.errMsg === "") {
            successIds.push(v.id);
            item.rotate = v.rotate;
        } else {
            errorIds.push(v.id);
            MessagePlugin.error(`${item.title}旋转失败：${v.errMsg}`);
        }
    });

    if (successIds.length === ids.length) {
        MessagePlugin.success("旋转成功");
    } else if (successIds.length && errorIds.length) {
        MessagePlugin.warning(`旋转成功${successIds.length}个，失败${errorIds.length}个`);
    } else {
        MessagePlugin.error("旋转失败");
    }
};

// 点击更多事件
const handleSelectMore = async (mod: string, item: any) => {
    switch (mod) {
        // 下载
        case "download":
            downloadFile(item.url);
            break;

        // 移动目录
        case "move":
            handleModifyDirectory(
                [item.id],
                [item.folderId],
            );
            break;

        // 旋转
        case "rotate90":
        case "rotate270":
            await handleBatchRotate([item.id], mod === "rotate90" ? 90 : 270);
            break;

        // 删除
        case "delete":
            handleDelete([item.id]);
            break;
    }
};

// 上传视频
const uploadVideoDialogVisible = ref(false);
</script>

<style lang="less" scoped>
.material-management-panel {
    height: 100%;
    display: flex;
    gap: 10px;

    > .panel-container {
        height: 100%;
        border-radius: 3px;
        background-color: #242b33;
        padding: 20px;
    }
}

.directory-wrapper {
    width: 240px;
}

.content-wrapper {
    flex: 1;
    display: flex;
    flex-direction: column;

    > .filter-section {
        display: flex;
        align-items: center;

        > .floatBox {
            flex: 1;
            display: flex;
            align-items: center;
            justify-content: flex-end;
            margin-left: 10px;
            gap: 10px;

            > .t-tree-select,
            > .t-select__wrap {
                width: 120px;
            }

            > .t-date-range-picker {
                width: 240px;
            }
        }
    }

    > .content {
        margin-top: 20px;
        flex: 1;
        overflow-y: overlay;

        > .videoCardList {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
            gap: 20px;
        }
    }

    > .t-pagination {
        margin-top: 10px;
        margin-bottom: -10px;
    }
}
</style>
